use crate::app_state::AppState;
use crate::handlers::account_handler::{get_user_info, ErrorResponse};
use crate::models::access_token::{
    CreateTokenRequest, CreateTokenResponse, RenameTokenRequest, TokenListItem,
};
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
};
use tracing::{error, info};

/// API令牌生成处理器
/// 处理 POST /auth/request_access_token 请求
pub async fn request_access_token_handler(
    State(app_state): State<AppState>,
    Json(payload): Json<CreateTokenRequest>,
) -> Result<Json<CreateTokenResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling POST /auth/request_access_token request");

    // 获取当前用户信息
    let user_info = match get_user_info().await {
        Ok(user) => user,
        Err(e) => {
            error!("Failed to get user info: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user info".to_string(),
                    message: Some(format!("User info retrieval failed: {}", e)),
                }),
            ));
        }
    };

    // 生成API令牌
    match app_state
        .token_service
        .generate_api_token(&user_info.user_id, payload.name)
        .await
    {
        Ok(token) => {
            info!(
                "API token generated successfully for user: {}",
                user_info.user_id
            );
            Ok(Json(CreateTokenResponse {
                access_token: token.access_token,
                expire_in: token.expire_in,
            }))
        }
        Err(e) => {
            error!("Failed to generate API token: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to generate access token".to_string(),
                    message: Some(format!("Token generation failed: {}", e)),
                }),
            ))
        }
    }
}

/// 获取API令牌列表处理器
/// 处理 GET /auth/access_token/_cat 请求
pub async fn list_access_tokens_handler(
    State(app_state): State<AppState>,
) -> Result<Json<Vec<TokenListItem>>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling GET /auth/access_token/_cat request");

    // 获取当前用户信息
    let user_info = match get_user_info().await {
        Ok(user) => user,
        Err(e) => {
            error!("Failed to get user info: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user info".to_string(),
                    message: Some(format!("User info retrieval failed: {}", e)),
                }),
            ));
        }
    };

    // 获取用户的所有令牌
    match app_state
        .token_service
        .get_user_tokens(&user_info.user_id)
        .await
    {
        Ok(tokens) => {
            info!(
                "Retrieved {} tokens for user: {}",
                tokens.len(),
                user_info.user_id
            );
            let token_list: Vec<TokenListItem> = tokens.into_iter().map(|t| t.into()).collect();
            Ok(Json(token_list))
        }
        Err(e) => {
            error!("Failed to get user tokens: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get access tokens".to_string(),
                    message: Some(format!("Token retrieval failed: {}", e)),
                }),
            ))
        }
    }
}

/// 删除API令牌处理器
/// 处理 DELETE /auth/access_token/{token_id} 请求
pub async fn delete_access_token_handler(
    State(app_state): State<AppState>,
    Path(token_id): Path<String>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling DELETE /auth/access_token/{} request", token_id);

    // 获取当前用户信息
    let user_info = match get_user_info().await {
        Ok(user) => user,
        Err(e) => {
            error!("Failed to get user info: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user info".to_string(),
                    message: Some(format!("User info retrieval failed: {}", e)),
                }),
            ));
        }
    };

    // 撤销令牌
    match app_state
        .token_service
        .revoke_token(&token_id, &user_info.user_id)
        .await
    {
        Ok(_) => {
            info!("Token deleted successfully: {}", token_id);
            Ok(Json(serde_json::json!({
                "result": "deleted",
                "id": token_id
            })))
        }
        Err(e) => {
            error!("Failed to delete token: {}", e);
            Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "Token not found or permission denied".to_string(),
                    message: Some(format!("Token deletion failed: {}", e)),
                }),
            ))
        }
    }
}

/// 重命名API令牌处理器
/// 处理 POST /auth/access_token/{token_id}/_rename 请求
pub async fn rename_access_token_handler(
    State(app_state): State<AppState>,
    Path(token_id): Path<String>,
    Json(payload): Json<RenameTokenRequest>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Handling POST /auth/access_token/{}/_rename request",
        token_id
    );

    // 验证请求参数
    if payload.name.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "name is required".to_string(),
                message: Some("Token name cannot be empty".to_string()),
            }),
        ));
    }

    // 获取当前用户信息
    let user_info = match get_user_info().await {
        Ok(user) => user,
        Err(e) => {
            error!("Failed to get user info: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user info".to_string(),
                    message: Some(format!("User info retrieval failed: {}", e)),
                }),
            ));
        }
    };

    // 重命名令牌
    match app_state
        .token_service
        .rename_token(&token_id, &user_info.user_id, payload.name)
        .await
    {
        Ok(_) => {
            info!("Token renamed successfully: {}", token_id);
            Ok(Json(serde_json::json!({
                "result": "updated",
                "id": token_id
            })))
        }
        Err(e) => {
            error!("Failed to rename token: {}", e);
            Err((
                StatusCode::NOT_FOUND,
                Json(ErrorResponse {
                    error: "Token not found or permission denied".to_string(),
                    message: Some(format!("Token rename failed: {}", e)),
                }),
            ))
        }
    }
}

/// 获取令牌统计信息处理器
/// 处理 GET /auth/access_token/_stats 请求
pub async fn get_token_stats_handler(
    State(app_state): State<AppState>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!("Handling GET /auth/access_token/_stats request");

    match app_state.token_service.get_token_stats().await {
        Ok(stats) => Ok(Json(serde_json::json!(stats))),
        Err(e) => {
            error!("Failed to get token stats: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get token statistics".to_string(),
                    message: Some("Database error occurred".to_string()),
                }),
            ))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_create_token_request_serialization() {
        let request = CreateTokenRequest {
            name: Some("test-token".to_string()),
        };

        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("test-token"));

        let deserialized: CreateTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, Some("test-token".to_string()));
    }

    #[tokio::test]
    async fn test_create_token_response_serialization() {
        let response = CreateTokenResponse {
            access_token: "test-token-123".to_string(),
            expire_in: 31536000,
        };

        let json = serde_json::to_string(&response).unwrap();
        assert!(json.contains("test-token-123"));
        assert!(json.contains("31536000"));

        let deserialized: CreateTokenResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.access_token, "test-token-123");
        assert_eq!(deserialized.expire_in, 31536000);
    }

    #[tokio::test]
    async fn test_rename_token_request_serialization() {
        let request = RenameTokenRequest {
            name: "new-name".to_string(),
        };

        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("new-name"));

        let deserialized: RenameTokenRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, "new-name");
    }
}
